"use client";
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import ProductCard from "@/components/ProductCard";
import { getProducts } from "@/apis/products";
import { getCategories } from "@/apis/categories";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

interface ProductsSectionProps {
  title: string;
  subtitle?: string;
  showCategories?: boolean;
  limit?: number;
  filter?: string; // "featured", "trending", "new", etc.
}

const ProductsSection: React.FC<ProductsSectionProps> = ({
  title,
  subtitle,
  showCategories = true,
  limit = 8,
  filter,
}) => {
  const [products, setProducts] = useState<ProductData[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<ProductData[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching products with filter:", filter);
        // Fetch products
        const fetchedProducts = await getProducts(filter);
        console.log("Fetched products:", fetchedProducts);

        setProducts(fetchedProducts);
        setFilteredProducts(fetchedProducts.slice(0, limit));

        // Extract unique categories from products
        if (showCategories) {
          // Get predefined categories
          const cats = getCategories().map((cat) => cat.name);

          // Also extract unique categories from products to ensure all are represented
          const productCategories = Array.from(
            new Set(fetchedProducts.map((product) => product.category))
          ).filter(Boolean); // Filter out any undefined/null/empty categories

          // Combine both sets of categories without duplicates
          const allCategories = Array.from(
            new Set(["all", ...cats, ...productCategories])
          );

          setCategories(allCategories);
        }
      } catch (error) {
        console.error("Error fetching products:", error);
        // Handle error state if needed
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [filter, limit, showCategories]);

  // Filter products by category
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);

    if (category === "all") {
      setFilteredProducts(products.slice(0, limit));
    } else {
      const filtered = products
        .filter(
          (product) => product.category.toLowerCase() === category.toLowerCase()
        )
        .slice(0, limit);
      setFilteredProducts(filtered);
    }
  };

  return (
    <section className="py-12 px-4 md:px-8">
      <div className="container mx-auto">
        {/* Section header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <motion.h2
              className="text-2xl md:text-3xl font-bold mb-2"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {title}
            </motion.h2>
            {subtitle && (
              <motion.p
                className="text-gray-600 max-w-2xl"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {subtitle}
              </motion.p>
            )}
          </div>

          <Link
            href="/products"
            className="group hidden md:flex items-center text-accent font-medium mt-4 md:mt-0"
          >
            View All
            <motion.span
              className="inline-block ml-1"
              initial={{ x: 0 }}
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 400 }}
            >
              <ArrowRight size={16} />
            </motion.span>
          </Link>
        </div>

        {/* Category filters */}
        {showCategories && categories.length > 0 && (
          <motion.div
            className="flex flex-wrap gap-2 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            {categories.map((category, index) => (
              <motion.button
                key={category}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                  selectedCategory === category
                    ? "bg-accent text-white"
                    : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                }`}
                onClick={() => handleCategoryChange(category)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </motion.button>
            ))}
          </motion.div>
        )}

        {/* Products grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-3">
            {[...Array(4)].map((_, index) => (
              <div
                key={index}
                className="bg-gray-100 rounded-lg h-80 animate-pulse"
              ></div>
            ))}
          </div>
        ) : (
          <AnimatePresence mode="wait">
            <motion.div
              key={selectedCategory}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
              className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-6"
            >
              {filteredProducts.length > 0 ? (
                filteredProducts.map((product, index) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    index={index}
                  />
                ))
              ) : (
                <div className="col-span-full text-center py-12">
                  <p className="text-gray-500">
                    No products found in this category.
                  </p>
                </div>
              )}
            </motion.div>
          </AnimatePresence>
        )}

        {/* Mobile view all link */}
        <div className="mt-8 text-center md:hidden">
          <Link
            href="/products"
            className="inline-flex items-center text-accent font-medium"
          >
            View All Products
            <ArrowRight size={16} className="ml-1" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ProductsSection;
